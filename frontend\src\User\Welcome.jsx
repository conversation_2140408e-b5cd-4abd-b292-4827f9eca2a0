import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaBook, FaShoppingCart, FaHeart, FaUser, FaSignOutAlt, FaHistory, FaClock, FaGift, FaArrowRight } from 'react-icons/fa';
import axios from 'axios';
import './BookStore.css';
import FloatingActions from '../components/FloatingActions';

const Welcome = () => {
  const [user, setUser] = useState(null);
  const [recentOrders, setRecentOrders] = useState([]);
  const [cartCount, setCartCount] = useState(0);
  const [wishlistCount, setWishlistCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const initializeWelcome = async () => {
      // Get user from localStorage
      const userData = localStorage.getItem('user');
      if (userData) {
        try {
          const parsedUser = JSON.parse(userData);
          setUser(parsedUser);

          // Fetch user's recent orders
          try {
            const ordersResponse = await axios.get(`http://localhost:4000/getorders/${parsedUser.id}`);
            setRecentOrders(ordersResponse.data.slice(0, 3)); // Get last 3 orders
          } catch (error) {
            console.error('Error fetching orders:', error);
            setRecentOrders([]);
          }

          // Get cart count from localStorage
          const cart = localStorage.getItem('cart');
          if (cart) {
            try {
              const cartItems = JSON.parse(cart);
              setCartCount(cartItems.length);
            } catch (e) {
              setCartCount(0);
            }
          }

          // Fetch wishlist count
          try {
            const wishlistResponse = await axios.get(`http://localhost:4000/wishlist/${parsedUser.id}`);
            setWishlistCount(wishlistResponse.data.length);
          } catch (error) {
            console.error('Error fetching wishlist:', error);
            setWishlistCount(0);
          }

        } catch (e) {
          console.error('Error parsing user data:', e);
          navigate('/login');
        }
      } else {
        navigate('/login');
      }
      setLoading(false);
    };

    initializeWelcome();
  }, [navigate]);

  const handleLogout = () => {
    localStorage.removeItem('user');
    navigate('/best-wishes');
  };

  const handleBrowseBooks = () => {
    navigate('/books');
  };

  if (loading || !user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-teal-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-teal-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <header className="bg-white shadow-lg border-b-2 border-teal-100">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <FaBook className="text-3xl text-teal-600" />
              <h1 className="text-2xl font-bold text-gray-800">BookStore</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-gray-700">
                <FaUser className="text-teal-600" />
                <span className="font-medium">{user.name}</span>
              </div>
              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              >
                <FaSignOutAlt />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-16">
        <div className="max-w-4xl mx-auto text-center">
          {/* Welcome Message */}
          <div className="mb-12">
            <h2 className="text-5xl font-bold text-gray-800 mb-4">
              Welcome back, <span className="text-teal-600">{user.name}!</span>
            </h2>
            <p className="text-xl text-gray-600 leading-relaxed">
              We're delighted to have you here. Discover amazing books, manage your collection, 
              and enjoy a seamless reading experience.
            </p>
          </div>

          {/* Action Cards */}
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            {/* Browse Books Card */}
            <div className="bg-white rounded-2xl shadow-xl p-8 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="text-center">
                <div className="bg-teal-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <FaBook className="text-3xl text-teal-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">Browse Books</h3>
                <p className="text-gray-600 mb-6">
                  Explore our vast collection of books across all genres. Find your next great read!
                </p>
                <button
                  onClick={handleBrowseBooks}
                  className="bg-teal-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-teal-700 transition-colors transform hover:scale-105"
                >
                  Start Browsing
                </button>
              </div>
            </div>

            {/* Quick Actions Card */}
            <div className="bg-white rounded-2xl shadow-xl p-8 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="text-center">
                <div className="bg-blue-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <FaShoppingCart className="text-3xl text-blue-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">Quick Actions</h3>
                <p className="text-gray-600 mb-6">
                  Access your cart, wishlist, and orders with just one click.
                </p>
                <div className="space-y-3">
                  <button
                    onClick={() => navigate('/mycart')}
                    className="w-full bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-between"
                  >
                    <span>My Cart</span>
                    {cartCount > 0 && (
                      <span className="bg-blue-800 text-white px-2 py-1 rounded-full text-xs">
                        {cartCount}
                      </span>
                    )}
                  </button>
                  <button
                    onClick={() => navigate('/wishlist')}
                    className="w-full bg-pink-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-pink-700 transition-colors flex items-center justify-between"
                  >
                    <span>Wishlist</span>
                    {wishlistCount > 0 && (
                      <span className="bg-pink-800 text-white px-2 py-1 rounded-full text-xs">
                        {wishlistCount}
                      </span>
                    )}
                  </button>
                  <button
                    onClick={() => navigate('/myorders-new')}
                    className="w-full bg-green-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center justify-between"
                  >
                    <span>My Orders</span>
                    {recentOrders.length > 0 && (
                      <span className="bg-green-800 text-white px-2 py-1 rounded-full text-xs">
                        {recentOrders.length}
                      </span>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Orders Section */}
          {recentOrders.length > 0 && (
            <div className="bg-white rounded-2xl shadow-xl p-8 mb-12">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-bold text-gray-800 flex items-center">
                  <FaHistory className="mr-3 text-blue-600" />
                  Recent Orders
                </h3>
                <button
                  onClick={() => navigate('/myorders-new')}
                  className="text-blue-600 hover:text-blue-800 font-medium flex items-center"
                >
                  View All <FaArrowRight className="ml-1" />
                </button>
              </div>

              <div className="grid md:grid-cols-3 gap-4">
                {recentOrders.map((order, index) => (
                  <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-start space-x-3">
                      <div className="w-12 h-16 bg-gray-200 rounded flex items-center justify-center">
                        <FaBook className="text-gray-500" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-800 text-sm mb-1">
                          {order.booktitle || 'Book Order'}
                        </h4>
                        <p className="text-gray-600 text-xs mb-1">
                          by {order.bookauthor || 'Unknown Author'}
                        </p>
                        <p className="text-green-600 font-bold text-sm">
                          ₹{order.totalamount}
                        </p>
                        <div className="flex items-center text-xs text-gray-500 mt-2">
                          <FaClock className="mr-1" />
                          {order.BookingDate}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Stats Section */}
          <div className="bg-white rounded-2xl shadow-xl p-8 mb-12">
            <h3 className="text-2xl font-bold text-gray-800 mb-6">Your BookStore Journey</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-4">
                <div className="text-3xl font-bold text-teal-600 mb-2">📚</div>
                <div className="text-lg font-semibold text-gray-800">Discover</div>
                <div className="text-gray-600">Thousands of books</div>
              </div>
              <div className="text-center p-4">
                <div className="text-3xl font-bold text-blue-600 mb-2">🛒</div>
                <div className="text-lg font-semibold text-gray-800">Shop</div>
                <div className="text-gray-600">Easy & secure checkout</div>
              </div>
              <div className="text-center p-4">
                <div className="text-3xl font-bold text-green-600 mb-2">📖</div>
                <div className="text-lg font-semibold text-gray-800">Enjoy</div>
                <div className="text-gray-600">Your reading adventure</div>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center">
            <p className="text-lg text-gray-600 mb-6">
              Ready to dive into the world of books?
            </p>
            <button
              onClick={handleBrowseBooks}
              className="bg-gradient-to-r from-teal-600 to-blue-600 text-white px-12 py-4 rounded-xl font-bold text-lg hover:from-teal-700 hover:to-blue-700 transition-all transform hover:scale-105 shadow-lg"
            >
              Explore Books Now
            </button>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8 mt-16">
        <div className="container mx-auto px-6 text-center">
          <p className="text-gray-300">
            © {new Date().getFullYear()} BookStore. Made with ❤️ for book lovers.
          </p>
        </div>
      </footer>

      {/* Floating Actions */}
      <FloatingActions />
    </div>
  );
};

export default Welcome;
