import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Card, Button } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import './BookStore.css'; // Create this CSS file for custom styles
import Footer from '../Components/Footer';

// Navbar for Book Store
function BookStoreNavbar() {
  const navigate = useNavigate();
  const user = JSON.parse(localStorage.getItem('user'));

  return (
    <nav className="navbar navbar-expand-lg navbar-light bg-light bookstore-navbar">
      <div className="container">
        <a className="navbar-brand" href="/">BookStore</a>
        <div>
          <Button variant="outline-primary" onClick={() => navigate('/myorders')}>My Orders</Button>
          {user ? (
            <Button variant="outline-danger" className="ms-2" onClick={() => {
              localStorage.removeItem('user');
              navigate('/best-wishes');
            }}>Logout</Button>
          ) : (
            <Button variant="outline-success" className="ms-2" onClick={() => navigate('/login')}>Login</Button>
          )}
        </div>
      </div>
    </nav>
  );
}

// Book List Page
function BookList() {
  const [books, setBooks] = useState([]);
  const navigate = useNavigate();

  useEffect(() => {
    axios.get('http://localhost:4000/books')
      .then(res => setBooks(res.data))
      .catch(err => console.error('Error fetching books:', err));
  }, []);

  return (
    <div className="container mt-4">
      <h1 className="text-center mb-4">Welcome to the Book Store</h1>
      <div className="row">
        {books.map(book => (
          <div className="col-md-3 mb-4" key={book._id}>
            <Card className="h-100 bookstore-card">
              <Card.Img variant="top" src={`/${book.coverImage}`} alt={book.title} style={{ height: '250px', objectFit: 'cover' }} />
              <Card.Body>
                <Card.Title>{book.title}</Card.Title>
                <Card.Text>
                  <strong>Author:</strong> {book.author}<br />
                  <strong>Price:</strong> ${book.price}
                </Card.Text>
                <Button variant="primary" onClick={() => navigate(`/book/${book._id}`)}>View Details</Button>
              </Card.Body>
            </Card>
          </div>
        ))}
      </div>
    </div>
  );
}

// Book Details Page
function BookDetails({ bookId }) {
  const [book, setBook] = useState(null);
  const [orderSuccess, setOrderSuccess] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    axios.get(`http://localhost:4000/books/${bookId}`)
      .then(res => setBook(res.data))
      .catch(err => console.error('Error fetching book:', err));
  }, [bookId]);

  const handleOrder = () => {
    const user = JSON.parse(localStorage.getItem('user'));
    if (!user) {
      navigate('/login');
      return;
    }
    axios.post('http://localhost:4000/order', {
      userId: user.id,
      bookId: book._id
    })
      .then(() => setOrderSuccess(true))
      .catch(err => alert('Order failed!'));
  };

  if (!book) return <div className="container mt-5">Loading...</div>;

  return (
    <div className="container mt-5">
      <div className="row">
        <div className="col-md-4">
          <img src={`/${book.coverImage}`} alt={book.title} className="img-fluid" />
        </div>
        <div className="col-md-8">
          <h2>{book.title}</h2>
          <p><strong>Author:</strong> {book.author}</p>
          <p><strong>Description:</strong> {book.description}</p>
          <p><strong>Price:</strong> ${book.price}</p>
          <Button variant="success" onClick={handleOrder}>Order Now</Button>
          {orderSuccess && <div className="alert alert-success mt-3">Order placed successfully!</div>}
        </div>
      </div>
    </div>
  );
}

// My Orders Page
function MyOrders() {
  const [orders, setOrders] = useState([]);
  const [books, setBooks] = useState({});
  const navigate = useNavigate();

  useEffect(() => {
    const user = JSON.parse(localStorage.getItem('user'));
    if (!user) {
      navigate('/login');
      return;
    }
    axios.get(`http://localhost:4000/getorders/${user.id}`)
      .then(res => {
        setOrders(res.data);
        // Fetch all books for mapping
        axios.get('http://localhost:4000/books')
          .then(bookRes => {
            const bookMap = {};
            bookRes.data.forEach(b => { bookMap[b._id] = b; });
            setBooks(bookMap);
          });
      })
      .catch(err => console.error('Error fetching orders:', err));
  }, [navigate]);

  return (
    <div>
      <BookStoreNavbar />
      <div className="container mt-4">
        <h1 className="text-center mb-4">My Orders</h1>
        {orders.length === 0 ? (
          <div className="text-center">No orders found.</div>
        ) : (
          <div className="row">
            {orders.map(order => {
              const book = books[order.bookId];
              return (
                <div className="col-md-6 mb-4" key={order._id}>
                  <Card className="bookstore-card">
                    <div className="d-flex align-items-center">
                      <img
                        src={book ? `/${book.coverImage}` : '/default_cover.jpg'}
                        alt={book ? book.title : 'Book'}
                        style={{ height: '100px', width: '80px', objectFit: 'cover', marginRight: '20px' }}
                      />
                      <div>
                        <h5>{book ? book.title : 'Book'}</h5>
                        <p><strong>Order ID:</strong> {order._id.slice(0, 10)}</p>
                        <p><strong>Price:</strong> ${book ? book.price : '--'}</p>
                        <p><strong>Status:</strong> {order.status || 'Processing'}</p>
                        <p><strong>Order Date:</strong> {order.orderDate ? new Date(order.orderDate).toLocaleDateString() : '--'}</p>
                      </div>
                    </div>
                  </Card>
                </div>
              );
            })}
          </div>
        )}
      </div>
      <Footer />
    </div>
  );
}

// Routing Setup
import { BrowserRouter as Router, Routes, Route, useParams } from 'react-router-dom';

function BookDetailsWrapper() {
  const { id } = useParams();
  return <BookDetails bookId={id} />;
}

function App() {
  return (
    <Router>
      <BookStoreNavbar />
      <Routes>
        <Route path="/" element={<BookList />} />
        <Route path="/book/:id" element={<BookDetailsWrapper />} />
        <Route path="/myorders" element={<MyOrders />} />
        {/* Add login/register routes as needed */}
      </Routes>
      <Footer />
    </Router>
  );
}

export default App;
