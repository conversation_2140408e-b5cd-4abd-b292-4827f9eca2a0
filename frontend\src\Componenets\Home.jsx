// src/components/Home.js - Dark Academia BookStore Welcome Page

import React from 'react';
import { Link } from "react-router-dom";
import { FaBook, FaUsers, FaStore, FaUserShield, FaBookOpen, FaQuoteLeft, FaArrowRight } from 'react-icons/fa';

const Home = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50">
      {/* Simple Dark Academia Navigation */}
      <nav className="bg-gradient-to-r from-amber-900 to-orange-800 shadow-lg">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Link to="/" className="flex items-center space-x-3 text-2xl font-bold text-amber-100 hover:text-white transition-colors font-serif-primary">
              <FaBook className="text-amber-200" />
              <span>BookStore</span>
            </Link>

            <div className="flex items-center space-x-3">
              <Link to="/login" className="flex items-center space-x-2 px-4 py-2 text-amber-100 hover:text-white hover:bg-amber-700 rounded-lg transition-all font-sans">
                <FaUsers className="text-sm" />
                <span>Customer</span>
              </Link>
              <Link to="/slogin" className="flex items-center space-x-2 px-4 py-2 text-amber-100 hover:text-white hover:bg-amber-700 rounded-lg transition-all font-sans">
                <FaStore className="text-sm" />
                <span>Seller</span>
              </Link>
              <Link to="/alogin" className="flex items-center space-x-2 px-4 py-2 text-amber-100 hover:text-white hover:bg-amber-700 rounded-lg transition-all font-sans">
                <FaUserShield className="text-sm" />
                <span>Admin</span>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Simple Welcome Section */}
      <main className="container mx-auto px-6 py-16">
        <div className="text-center max-w-4xl mx-auto">
          {/* Welcome Header */}
          <div className="mb-12">
            <FaBookOpen className="text-6xl text-amber-700 mx-auto mb-6" />
            <h1 className="text-5xl font-bold text-amber-900 mb-4 font-serif-primary">
              Welcome to BookStore
            </h1>
            <p className="text-xl text-amber-700 font-sans leading-relaxed">
              Discover a world of knowledge and imagination in our carefully curated collection of books
            </p>
          </div>

          {/* Inspirational Quote */}
          <div className="bg-white/70 backdrop-blur-sm rounded-lg p-8 mb-12 shadow-lg border border-amber-200">
            <FaQuoteLeft className="text-3xl text-amber-600 mb-4 mx-auto" />
            <blockquote className="text-lg text-amber-800 font-serif-secondary italic mb-4">
              "A room without books is like a body without a soul."
            </blockquote>
            <cite className="text-amber-600 font-sans">— Marcus Tullius Cicero</cite>
          </div>

          {/* Simple Action Cards */}
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <Link to="/login" className="group">
              <div className="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-amber-200 hover:border-amber-300 hover:-translate-y-1">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-700 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-105 transition-transform">
                  <FaUsers className="text-white text-2xl" />
                </div>
                <h3 className="text-xl font-bold text-amber-900 mb-2 font-serif-primary">Browse Books</h3>
                <p className="text-amber-700 text-sm font-sans mb-4">
                  Discover and purchase from our curated collection
                </p>
                <div className="flex items-center justify-center text-blue-600 font-semibold group-hover:text-blue-700 transition-colors">
                  <span>Get Started</span>
                  <FaArrowRight className="ml-2 text-sm" />
                </div>
              </div>
            </Link>

            <Link to="/slogin" className="group">
              <div className="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-amber-200 hover:border-amber-300 hover:-translate-y-1">
                <div className="w-16 h-16 bg-gradient-to-r from-green-600 to-emerald-700 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-105 transition-transform">
                  <FaStore className="text-white text-2xl" />
                </div>
                <h3 className="text-xl font-bold text-amber-900 mb-2 font-serif-primary">Sell Books</h3>
                <p className="text-amber-700 text-sm font-sans mb-4">
                  List your books and reach thousands of readers
                </p>
                <div className="flex items-center justify-center text-green-600 font-semibold group-hover:text-green-700 transition-colors">
                  <span>Start Selling</span>
                  <FaArrowRight className="ml-2 text-sm" />
                </div>
              </div>
            </Link>

            <Link to="/alogin" className="group">
              <div className="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-amber-200 hover:border-amber-300 hover:-translate-y-1">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-violet-700 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-105 transition-transform">
                  <FaUserShield className="text-white text-2xl" />
                </div>
                <h3 className="text-xl font-bold text-amber-900 mb-2 font-serif-primary">Admin Panel</h3>
                <p className="text-amber-700 text-sm font-sans mb-4">
                  Manage the platform and oversee operations
                </p>
                <div className="flex items-center justify-center text-purple-600 font-semibold group-hover:text-purple-700 transition-colors">
                  <span>Access Panel</span>
                  <FaArrowRight className="ml-2 text-sm" />
                </div>
              </div>
            </Link>
          </div>

          {/* Simple Footer */}
          <div className="mt-16 text-center">
            <p className="text-amber-600 font-sans">
              © 2024 BookStore. Cultivating minds through literature.
            </p>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Home;
