// src/components/Home.js - Dark Academia BookStore Landing Page

import React from 'react';
import { Link } from "react-router-dom";
import { FaBook, FaUsers, FaStore, FaUserShield } from 'react-icons/fa';
import HeroBanner from '../components/HeroBanner';
import FeaturedBooks from '../components/FeaturedBooks';
import CategoriesNav from '../components/CategoriesNav';
import FloatingActions from '../components/FloatingActions';
import HeaderCart from '../components/HeaderCart';

const Home = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50">
      {/* Dark Academia Navigation */}
      <nav className="bg-gradient-to-r from-amber-800 to-orange-700 shadow-2xl border-b-4 border-amber-600 sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Link to="/" className="flex items-center space-x-3 text-3xl font-bold text-amber-100 hover:text-white transition-colors font-serif-primary">
              <FaBook className="text-amber-200" />
              <span>BookStore</span>
            </Link>

            {/* Header Cart Section */}
            <div className="hidden lg:block">
              <HeaderCart />
            </div>

            <div className="hidden md:flex items-center space-x-4">
              <Link to="/login" className="flex items-center space-x-2 px-6 py-3 text-amber-100 hover:text-white hover:bg-amber-700 rounded-lg transition-all font-sans font-semibold">
                <FaUsers />
                <span>Customer</span>
              </Link>
              <Link to="/slogin" className="flex items-center space-x-2 px-6 py-3 text-amber-100 hover:text-white hover:bg-amber-700 rounded-lg transition-all font-sans font-semibold">
                <FaStore />
                <span>Seller</span>
              </Link>
              <Link to="/alogin" className="flex items-center space-x-2 px-6 py-3 text-amber-100 hover:text-white hover:bg-amber-700 rounded-lg transition-all font-sans font-semibold">
                <FaUserShield />
                <span>Admin</span>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Banner */}
      <HeroBanner />

      {/* Categories Navigation */}
      <CategoriesNav />

      {/* Featured Books Section */}
      <FeaturedBooks />

      {/* Action Cards Section */}
      <section className="py-16 bg-gradient-to-b from-amber-50 to-orange-100">
        <div className="container mx-auto px-6">
          <div className="text-center max-w-4xl mx-auto mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-amber-900 mb-6 leading-tight font-serif-primary">
              Join Our Literary Community
            </h2>
            <p className="text-xl text-amber-700 leading-relaxed font-serif-secondary">
              Whether you're a passionate reader, aspiring author, or business owner,
              find your place in our thriving book ecosystem.
            </p>
          </div>

          {/* Action Cards */}
          <div className="grid md:grid-cols-3 gap-8">
            <Link to="/login" className="group">
              <div className="bg-white/90 backdrop-blur-sm p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border-2 border-amber-200 hover:border-amber-300 hover:-translate-y-2">
                <div className="w-20 h-20 bg-gradient-to-r from-blue-600 to-indigo-700 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform shadow-lg">
                  <FaUsers className="text-white text-3xl" />
                </div>
                <h3 className="text-2xl font-bold text-amber-900 mb-4 font-serif-primary">Browse & Buy</h3>
                <p className="text-amber-700 leading-relaxed font-sans">
                  Explore thousands of carefully curated books, create your personal wishlist,
                  and purchase your next great read with our seamless checkout experience.
                </p>
                <div className="mt-6 text-blue-600 font-semibold group-hover:text-blue-700 transition-colors">
                  Start Reading →
                </div>
              </div>
            </Link>

            <Link to="/slogin" className="group">
              <div className="bg-white/90 backdrop-blur-sm p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border-2 border-amber-200 hover:border-amber-300 hover:-translate-y-2">
                <div className="w-20 h-20 bg-gradient-to-r from-green-600 to-emerald-700 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform shadow-lg">
                  <FaStore className="text-white text-3xl" />
                </div>
                <h3 className="text-2xl font-bold text-amber-900 mb-4 font-serif-primary">Sell Books</h3>
                <p className="text-amber-700 leading-relaxed font-sans">
                  Transform your passion for books into profit. List your inventory,
                  manage orders, and connect with readers through our comprehensive seller platform.
                </p>
                <div className="mt-6 text-green-600 font-semibold group-hover:text-green-700 transition-colors">
                  Start Selling →
                </div>
              </div>
            </Link>

            <Link to="/alogin" className="group">
              <div className="bg-white/90 backdrop-blur-sm p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border-2 border-amber-200 hover:border-amber-300 hover:-translate-y-2">
                <div className="w-20 h-20 bg-gradient-to-r from-purple-600 to-violet-700 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform shadow-lg">
                  <FaUserShield className="text-white text-3xl" />
                </div>
                <h3 className="text-2xl font-bold text-amber-900 mb-4 font-serif-primary">Admin Panel</h3>
                <p className="text-amber-700 leading-relaxed font-sans">
                  Oversee the entire marketplace ecosystem. Manage users, moderate content,
                  and ensure a premium experience for all community members.
                </p>
                <div className="mt-6 text-purple-600 font-semibold group-hover:text-purple-700 transition-colors">
                  Manage Platform →
                </div>
              </div>
            </Link>
          </div>
        </div>
      </section>

      {/* Floating Actions */}
      <FloatingActions />

      {/* Mobile Menu */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-gradient-to-r from-amber-800 to-orange-700 border-t-4 border-amber-600 p-4 shadow-2xl">
        <div className="flex justify-around">
          <Link to="/login" className="flex flex-col items-center space-y-1 text-amber-100 hover:text-white transition-colors">
            <FaUsers className="text-xl" />
            <span className="text-xs font-semibold">Customer</span>
          </Link>
          <Link to="/slogin" className="flex flex-col items-center space-y-1 text-amber-100 hover:text-white transition-colors">
            <FaStore className="text-xl" />
            <span className="text-xs font-semibold">Seller</span>
          </Link>
          <Link to="/alogin" className="flex flex-col items-center space-y-1 text-amber-100 hover:text-white transition-colors">
            <FaUserShield className="text-xl" />
            <span className="text-xs font-semibold">Admin</span>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Home;
