import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaShoppingCart, FaClipboardList, FaHeart, FaComments } from 'react-icons/fa';
import FeedbackModal from './FeedbackModal';

const FloatingActions = () => {
  const [cartCount, setCartCount] = useState(0);
  const [wishlistCount, setWishlistCount] = useState(0);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // Get cart count from localStorage
    const updateCounts = () => {
      try {
        const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]');
        const wishlistItems = JSON.parse(localStorage.getItem('wishlistItems') || '[]');
        setCartCount(cartItems.length);
        setWishlistCount(wishlistItems.length);
      } catch (error) {
        console.error('Error reading from localStorage:', error);
        setCartCount(0);
        setWishlistCount(0);
      }
    };

    updateCounts();

    // Listen for storage changes
    const handleStorageChange = () => {
      updateCounts();
    };

    window.addEventListener('storage', handleStorageChange);
    
    // Custom event for cart updates
    window.addEventListener('cartUpdated', handleStorageChange);
    window.addEventListener('wishlistUpdated', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('cartUpdated', handleStorageChange);
      window.removeEventListener('wishlistUpdated', handleStorageChange);
    };
  }, []);

  const handleCartClick = () => {
    navigate('/cart');
  };

  const handleOrdersClick = () => {
    navigate('/myorders');
  };

  const handleWishlistClick = () => {
    navigate('/wishlist');
  };

  return (
    <>
    <div className="floating-actions">
      {/* Cart Button */}
      <button 
        className="fab cart-fab"
        onClick={handleCartClick}
        title="My Cart"
      >
        <FaShoppingCart />
        {cartCount > 0 && (
          <span className="fab-badge">
            {cartCount > 99 ? '99+' : cartCount}
          </span>
        )}
      </button>

      {/* Wishlist Button */}
      <button 
        className="fab wishlist-fab"
        onClick={handleWishlistClick}
        title="My Wishlist"
        style={{
          background: 'linear-gradient(135deg, #dc2626, #b91c1c)'
        }}
      >
        <FaHeart />
        {wishlistCount > 0 && (
          <span className="fab-badge">
            {wishlistCount > 99 ? '99+' : wishlistCount}
          </span>
        )}
      </button>

      {/* Orders Button */}
      <button
        className="fab orders-fab"
        onClick={handleOrdersClick}
        title="My Orders"
      >
        <FaClipboardList />
      </button>

      {/* Feedback Button */}
      <button
        className="fab feedback-fab"
        onClick={() => setShowFeedbackModal(true)}
        title="Send Feedback"
        style={{
          background: 'linear-gradient(135deg, #7c3aed, #5b21b6)'
        }}
      >
        <FaComments />
      </button>
    </div>

    {/* Feedback Modal */}
    <FeedbackModal
      isOpen={showFeedbackModal}
      onClose={() => setShowFeedbackModal(false)}
    />
  </>
  );
};

export default FloatingActions;
