import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";

const AdminLogin = () => {
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [error, setError] = useState("");
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();

    axios.defaults.withCredentials = true;

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError("");
        setLoading(true);

        if (!email.trim() || !password.trim()) {
            setError("Please fill in all fields");
            setLoading(false);
            return;
        }

        try {
            const payload = { email: email.trim(), password };
            const response = await axios.post("http://localhost:4000/alogin", payload);

            if (response.data.Status === "Success") {
                localStorage.setItem("user", JSON.stringify(response.data.user));
                alert("Admin login successful!");
                navigate("/ahome");
            } else {
                setError(response.data || "Invalid credentials");
            }
        } catch (err) {
            console.error("Admin login error:", err);
            setError("Login failed. Please check your connection and try again.");
        } finally {
            setLoading(false);
        }
    };

    const handleSignup = (e) => {
        e.preventDefault();
        navigate("/asignup");
    };

    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-indigo-100 to-purple-100">
            <header className="w-full py-6 bg-indigo-600 shadow-md mb-8">
                <div className="container mx-auto flex items-center justify-between px-4">
                    <h1 className="text-2xl font-bold text-white cursor-pointer" onClick={() => navigate("/")}>
                        BookStore Admin
                    </h1>
                    <nav>
                        <button
                            className="text-white font-semibold hover:underline mr-4"
                            onClick={() => navigate("/")}
                        >
                            Home
                        </button>
                        <button
                            className="text-white font-semibold hover:underline"
                            onClick={handleSignup}
                        >
                            Admin Signup
                        </button>
                    </nav>
                </div>
            </header>
            
            <div className="w-full max-w-md bg-white rounded-lg shadow-lg p-8">
                <div className="text-center mb-8">
                    <h2 className="text-3xl font-bold text-gray-800 mb-2">Admin Login</h2>
                    <p className="text-gray-600">Access the admin dashboard</p>
                </div>

                {error && (
                    <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                        {error}
                    </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                    <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                            Admin Email
                        </label>
                        <input
                            id="email"
                            type="email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                            placeholder="Enter your admin email"
                            required
                        />
                    </div>

                    <div>
                        <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                            Password
                        </label>
                        <input
                            id="password"
                            type="password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                            placeholder="Enter your password"
                            required
                        />
                    </div>

                    <button
                        type="submit"
                        className="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors duration-300"
                        disabled={loading}
                    >
                        {loading ? "Logging in..." : "Login"}
                    </button>
                </form>
            </div>
        </div>
    );
};

export default AdminLogin;
