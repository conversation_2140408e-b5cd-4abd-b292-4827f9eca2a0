import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaShoppingCart, FaHistory, FaDollarSign } from 'react-icons/fa';

const HeaderCart = () => {
  const [cartCount, setCartCount] = useState(0);
  const [totalAmount, setTotalAmount] = useState(0);
  const navigate = useNavigate();

  useEffect(() => {
    const updateCartInfo = () => {
      try {
        const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]');
        setCartCount(cartItems.length);
        
        // Calculate total amount
        const total = cartItems.reduce((sum, item) => {
          const price = parseFloat(item.price || 0);
          const quantity = parseInt(item.quantity || 1);
          return sum + (price * quantity);
        }, 0);
        
        setTotalAmount(total);
      } catch (error) {
        console.error('Error reading cart from localStorage:', error);
        setCartCount(0);
        setTotalAmount(0);
      }
    };

    updateCartInfo();

    // Listen for cart updates
    const handleCartUpdate = () => {
      updateCartInfo();
    };

    window.addEventListener('storage', handleCartUpdate);
    window.addEventListener('cartUpdated', handleCartUpdate);

    return () => {
      window.removeEventListener('storage', handleCartUpdate);
      window.removeEventListener('cartUpdated', handleCartUpdate);
    };
  }, []);

  const handleCartClick = () => {
    navigate('/cart');
  };

  const handleOrdersClick = () => {
    navigate('/myorders');
  };

  return (
    <div className="header-cart-section">
      {/* Cart Preview */}
      <div 
        className="cart-preview"
        onClick={handleCartClick}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => e.key === 'Enter' && handleCartClick()}
      >
        <div className="flex items-center space-x-2">
          <FaShoppingCart className="text-amber-700" />
          <span className="text-amber-900 font-semibold">
            My Cart ({cartCount})
          </span>
          {totalAmount > 0 && (
            <div className="flex items-center text-green-600 font-bold">
              <FaDollarSign className="text-sm" />
              <span>{totalAmount.toFixed(2)}</span>
            </div>
          )}
        </div>
      </div>

      {/* Orders Link */}
      <div 
        className="orders-link"
        onClick={handleOrdersClick}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => e.key === 'Enter' && handleOrdersClick()}
      >
        <div className="flex items-center space-x-2">
          <FaHistory className="text-blue-700" />
          <span className="text-blue-900 font-semibold">My Orders</span>
        </div>
      </div>
    </div>
  );
};

export default HeaderCart;
