import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

// BookStore Seller Login Page
const SellerLogin = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const navigate = useNavigate();

  axios.defaults.withCredentials = true;

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const payload = { email, password };
      const res = await axios.post("http://localhost:4000/slogin", payload);
      if (res.data.Status === "Success") {
        localStorage.setItem('user', JSON.stringify(res.data.user));
        alert("Login successful");
        navigate('/shome');
      } else {
        alert("Wrong credentials");
      }
    } catch (err) {
      alert("Error logging in. Please try again.");
    }
  };

  const handleSignup = (e) => {
    e.preventDefault();
    navigate("/ssignup");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-100 to-blue-200 flex flex-col">
      <header className="bg-white shadow py-4 px-8 flex items-center justify-between">
        <div className="flex items-center">
          <img src="/bookstore-logo.png" alt="Book Store Logo" className="h-10 w-10 mr-2" />
          <span className="text-2xl font-bold text-indigo-700">BookStore</span>
        </div>
        <nav>
          <button
            className="text-indigo-600 hover:underline mr-4"
            onClick={() => navigate('/')}
          >
            Home
          </button>
          <button
            className="text-indigo-600 hover:underline"
            onClick={() => navigate('/books')}
          >
            Browse Books
          </button>
        </nav>
      </header>
      <main className="flex flex-1 items-center justify-center">
        <div className="w-full max-w-md bg-white rounded-lg shadow-lg p-8 relative">
          <h2 className="text-3xl font-bold text-center text-indigo-700 mb-6">
            Seller Login
          </h2>
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="mt-1 p-2 block w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-400"
                placeholder="Email address"
              />
            </div>
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="mt-1 p-2 block w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-400"
                placeholder="Password"
              />
            </div>
            <div>
              <button
                type="submit"
                className="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded transition-all duration-300"
              >
                Log in
              </button>
            </div>
          </form>
          <div className="mt-4 text-center">
            <span className="text-gray-600 text-sm">
              Don't have a seller account?
            </span>
            <button
              onClick={handleSignup}
              className="ml-2 text-indigo-600 hover:underline text-sm"
            >
              Signup
            </button>
          </div>
          <div className="absolute -top-8 right-4">
            <img src="/seller-login-cover.jpg" alt="Seller Login" className="h-16 w-16 rounded-full shadow-lg object-cover" />
          </div>
        </div>
      </main>
      <footer className="text-center py-4 text-gray-500 text-sm">
        &copy; {new Date().getFullYear()} BookStore. All rights reserved.
      </footer>
    </div>
  );
};

export default SellerLogin;
