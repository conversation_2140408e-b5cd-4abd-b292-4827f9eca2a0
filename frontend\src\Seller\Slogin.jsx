import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaStore, FaEye, FaEyeSlash, FaUser, FaLock, FaSignOutAlt } from 'react-icons/fa';
import axios from 'axios';

// BookStore Seller Login Page
const SellerLogin = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('seller123');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const navigate = useNavigate();

  // Check if seller is already logged in
  useEffect(() => {
    const seller = localStorage.getItem('seller');
    if (seller) {
      setIsLoggedIn(true);
      setCurrentUser(JSON.parse(seller));
    }
  }, []);

  axios.defaults.withCredentials = true;

  const handleLogout = () => {
    localStorage.removeItem('seller');
    setIsLoggedIn(false);
    setCurrentUser(null);
    alert('Logged out successfully!');
    navigate('/');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const payload = { email, password };
      const res = await axios.post("http://localhost:4000/slogin", payload);
      if (res.data.Status === "Success") {
        localStorage.setItem('user', JSON.stringify(res.data.user));
        alert("Login successful");
        navigate('/shome');
      } else {
        alert("Wrong credentials");
      }
    } catch (err) {
      alert("Error logging in. Please try again.");
    }
  };

  const handleSignup = (e) => {
    e.preventDefault();
    navigate("/ssignup");
  };

  return (
    <div className="login-container">
      <div className="login-card">
        {/* Compact Header */}
        <div className="login-header">
          <div className="flex justify-between items-start w-full">
            <div className="flex-1">
              <div className="login-icon" style={{ background: 'linear-gradient(135deg, #16a34a, #15803d)' }}>
                <FaStore className="text-white text-2xl" />
              </div>
              <h2 className="text-2xl font-bold text-amber-900 font-serif-primary">
                {isLoggedIn ? `Welcome, ${currentUser?.name || 'Seller'}` : 'Seller Portal'}
              </h2>
              <p className="text-amber-700 text-sm font-sans">
                {isLoggedIn ? 'You are currently logged in' : 'Access your seller dashboard'}
              </p>
            </div>
            {isLoggedIn && (
              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 bg-red-100 text-red-700 px-3 py-2 rounded-lg text-sm font-semibold hover:bg-red-200 transition-colors"
              >
                <FaSignOutAlt />
                <span>Logout</span>
              </button>
            )}
          </div>
        </div>

        {/* Demo Credentials Display */}
        <div className="demo-credentials">
          <p className="text-xs text-amber-800 font-semibold mb-2">Demo Seller Credentials:</p>
          <div className="text-xs text-amber-700 space-y-1">
            <div className="demo-credential-row">
              <span>Email:</span>
              <code className="demo-credential-code"><EMAIL></code>
            </div>
            <div className="demo-credential-row">
              <span>Password:</span>
              <code className="demo-credential-code">seller123</code>
            </div>
          </div>
        </div>

        {/* Compact Form */}
        <form className="space-y-4" onSubmit={handleSubmit}>
          <div className="relative">
            <FaUser className="absolute left-4 top-1/2 transform -translate-y-1/2 text-green-600" />
            <input
              type="email"
              placeholder="Seller email address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="login-input pl-12"
              required
            />
          </div>

          <div className="relative">
            <FaLock className="absolute left-4 top-1/2 transform -translate-y-1/2 text-green-600" />
            <input
              type={showPassword ? "text" : "password"}
              placeholder="Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="login-input pl-12 pr-12"
              required
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-green-600 hover:text-green-800"
            >
              {showPassword ? <FaEyeSlash /> : <FaEye />}
            </button>
          </div>

          <button
            type="submit"
            className="login-button"
            style={{
              background: 'linear-gradient(135deg, #16a34a, #15803d)',
              boxShadow: '0 4px 15px rgba(22, 163, 74, 0.3)'
            }}
          >
            Access Seller Dashboard
          </button>
        </form>

        {/* Quick Actions */}
        <div className="mt-6 flex justify-between text-xs">
          <button
            onClick={() => navigate('/')}
            className="text-amber-600 hover:text-amber-800 transition-colors"
          >
            ← Back to Home
          </button>
          <button
            onClick={handleSignup}
            className="text-green-600 hover:text-green-800 transition-colors"
          >
            Become a Seller
          </button>
        </div>

        {/* Alternative Login Options */}
        <div className="mt-6 pt-4 border-t border-amber-200">
          <p className="text-center text-xs text-amber-700 mb-3">Or continue as:</p>
          <div className="flex space-x-2">
            <button
              onClick={() => navigate('/login')}
              className="flex-1 bg-blue-100 text-blue-700 py-2 px-3 rounded-lg text-xs font-semibold
                        hover:bg-blue-200 transition-colors"
            >
              Customer
            </button>
            <button
              onClick={() => navigate('/alogin')}
              className="flex-1 bg-purple-100 text-purple-700 py-2 px-3 rounded-lg text-xs font-semibold
                        hover:bg-purple-200 transition-colors"
            >
              Admin
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SellerLogin;
